"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ColumnData } from "../definitions/app.types";

interface ColumnComparisonTableProps {
  pageTypes: string[];
  columns: ColumnData[];
  columnsLoading: boolean;
  visualLoading: boolean;
  columnsError: string | null;
  visualError: string | null;
}

export default function ColumnComparisonTable({
  pageTypes,
  columns,
  columnsLoading,
  visualLoading,
  columnsError,
  visualError,
}: ColumnComparisonTableProps) {
  const isLoading = columnsLoading || visualLoading;
  const hasError = columnsError || visualError;

  if (hasError) {
    return (
      <Alert className="mb-4">
        <AlertDescription>
          {columnsError && <div>Columns Error: {columnsError}</div>}
          {visualError && <div>Visual Data Error: {visualError}</div>}
        </AlertDescription>
      </Alert>
    );
  }

  if (isLoading) {
    return (
      <Alert className="mb-4">
        <AlertDescription>
          Loading data...
          {visualLoading && " (Loading visual.json)"}
          {columnsLoading && " (Loading columns from backend)"}
        </AlertDescription>
      </Alert>
    );
  }

  // Get the maximum number of rows needed
  const maxRows = Math.max(pageTypes.length, columns.length);

  if (maxRows === 0) {
    return (
      <Alert className="mb-4">
        <AlertDescription>
          No data available. Please select a portal and ensure visual.json
          exists, then fetch columns from backend.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      {/* Summary */}
      <div className="grid grid-cols-2 gap-4">
        <Alert>
          <AlertDescription>
            <strong>Page Types:</strong> {pageTypes.length} unique types found
            in visual.json
          </AlertDescription>
        </Alert>
        <Alert>
          <AlertDescription>
            <strong>Columns:</strong> {columns.length} columns found in backend
          </AlertDescription>
        </Alert>
      </div>

      {/* Comparison Table */}
      <div className="overflow-x-auto w-full">
        <Table className="w-full table-fixed">
          <TableHeader>
            <TableRow>
              <TableHead className="w-[5%]">#</TableHead>
              <TableHead className="w-[47.5%]">
                Page Type (from visual.json)
              </TableHead>
              <TableHead className="w-[47.5%]">
                Column Slug (from backend)
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: maxRows }, (_, index) => {
              const pageType = pageTypes[index];
              const column = columns[index];

              return (
                <TableRow key={index}>
                  <TableCell className="text-center">{index + 1}</TableCell>
                  <TableCell className="truncate" title={pageType || ""}>
                    {pageType ? (
                      <Badge variant="secondary" className="max-w-full">
                        {pageType}
                      </Badge>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell className="truncate" title={column?.slug || ""}>
                    {column ? (
                      <div className="space-y-1">
                        <Badge variant="outline" className="max-w-full">
                          {column.slug}
                        </Badge>
                        <div
                          className="text-xs text-muted-foreground truncate"
                          title={column.title}
                        >
                          {column.title}
                        </div>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>

      {/* Additional Column Details */}
      {columns.length > 0 && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-3">Column Details</h3>
          <div className="grid gap-2">
            {columns.map((column) => (
              <div
                key={column.id}
                className="p-3 border rounded-md bg-muted/30"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <span className="font-medium">{column.slug}</span>
                    <span className="text-sm text-muted-foreground ml-2">
                      ({column.title})
                    </span>
                  </div>
                  <div className="flex gap-2">
                    <Badge variant={column.isActive ? "default" : "secondary"}>
                      {column.isActive ? "Active" : "Inactive"}
                    </Badge>
                    {column.isHidden ? (
                      <Badge variant="destructive">Hidden</Badge>
                    ) : null}
                  </div>
                </div>
                {column.lead && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {column.lead}
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
