"use client";

import { useState } from "react";
import { environments, portals } from "@/app/constants/app.consts";
import { Selector } from "@/app/components/Selector";
import LoginForm from "@/app/components/LoginForm";
import SectionCard from "@/app/components/SectionCard";
import ColumnComparisonTable from "@/app/components/ColumnComparisonTable";
import { useColumnComparison } from "@/app/hooks/useColumnComparison";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";

export default function ColumnsPage() {
  const [environment, setEnvironment] = useState(environments[0].value);
  const [selectedPortal, setSelectedPortal] = useState(
    portals?.[environment]?.[0]
  );
  const [authToken, setAuthToken] = useState("");

  const {
    columns,
    pageTypes,
    columnsLoading,
    visualLoading,
    columnsError,
    visualError,
    fetchColumns,
    fetchVisualAds,
  } = useColumnComparison(selectedPortal, authToken, environment);

  const onSelectEnvironment = (environment: string) => {
    setEnvironment(environment);
    setAuthToken("");
    setSelectedPortal(portals?.[environment]?.[0]);
  };

  const onSelectPortal = (portal: any) => {
    setSelectedPortal(portal);
  };

  const handleRefreshData = () => {
    fetchVisualAds();
    if (authToken) {
      fetchColumns();
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Column Comparison Tool</h1>
        <p className="text-muted-foreground">
          Compare page types from visual.json with column slugs from the backend API
        </p>
      </div>

      {/* Environment and Portal Selection */}
      <SectionCard
        number={1}
        title="Environment & Portal Selection"
        description="Select the environment and portal to compare data."
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Selector
            label="Environment"
            options={environments}
            selectedValue={environment}
            onSelect={onSelectEnvironment}
          />
          <Selector
            label="Portal"
            options={portals[environment] || []}
            selectedValue={selectedPortal}
            onSelect={onSelectPortal}
            valueKey="portal"
            labelKey="label"
          />
        </div>
      </SectionCard>

      {/* Authentication */}
      <SectionCard
        number={2}
        title="Authentication"
        description="Login or provide auth token to access backend data."
      >
        <LoginForm
          selectedPortal={selectedPortal}
          environment={environment}
          authToken={authToken}
          setAuthToken={setAuthToken}
        />
      </SectionCard>

      {/* Data Actions */}
      <SectionCard
        number={3}
        title="Data Actions"
        description="Load visual.json data and fetch columns from backend."
      >
        <div className="flex gap-4">
          <Button
            onClick={handleRefreshData}
            disabled={visualLoading || columnsLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${(visualLoading || columnsLoading) ? 'animate-spin' : ''}`} />
            Refresh Data
          </Button>
          <Button
            onClick={fetchColumns}
            disabled={!authToken || columnsLoading}
            variant="outline"
          >
            {columnsLoading ? "Loading..." : "Fetch Columns"}
          </Button>
        </div>
      </SectionCard>

      {/* Comparison Table */}
      <SectionCard
        number={4}
        title="Comparison Table"
        description="Side-by-side comparison of page types and column slugs."
      >
        <ColumnComparisonTable
          pageTypes={pageTypes}
          columns={columns}
          columnsLoading={columnsLoading}
          visualLoading={visualLoading}
          columnsError={columnsError}
          visualError={visualError}
        />
      </SectionCard>
    </div>
  );
}
