import { NextRequest, NextResponse } from "next/server";
import { getProtocolByEnv } from "@/app/utils/protocol-by-environment.util";

export async function POST(request: NextRequest) {
  try {
    const { token, portal, url, environment } = await request.json();

    if (!token || !portal || !url || !environment) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    const protocol = getProtocolByEnv(environment);
    const fullUrl = `${protocol}://${url}/api/columns`;

    console.log(`Fetching columns from: ${fullUrl}`);

    const response = await fetch(fullUrl, {
      method: "GET",
      headers: { 
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Columns API error: ${response.status} - ${errorText}`);
      return NextResponse.json(
        {
          error: `Failed to fetch columns: ${response.status} ${response.statusText}`,
          details: errorText,
        },
        { status: response.status }
      );
    }

    const data = await response.json();

    // The API should return an array of column objects
    // Each column should have: id, slug, title, isActive, etc.
    if (!Array.isArray(data)) {
      console.error("Unexpected response format:", data);
      return NextResponse.json(
        { error: "Unexpected response format from columns API" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      columns: data,
      count: data.length,
    });
  } catch (error) {
    console.error("Error fetching columns:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
